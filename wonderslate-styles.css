/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #0f172a;
    background-color: #ffffff;
    overflow-x: hidden;
    font-size: 16px;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #0f172a;
}

h1 {
    font-size: 4rem;
    font-weight: 900;
    letter-spacing: -0.02em;
}

h2 {
    font-size: 3rem;
    font-weight: 800;
    letter-spacing: -0.02em;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

p {
    margin-bottom: 1rem;
    color: #64748b;
    font-size: 1.125rem;
    line-height: 1.7;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(255, 107, 53, 0.25);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px 0 rgba(255, 107, 53, 0.35);
}

.btn-secondary {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 107, 53, 0.15);
    border-color: rgba(255, 107, 53, 0.3);
}

.btn i {
    font-size: 14px;
}

/* Badges */
.hero-badge, .section-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.badge-text {
    position: relative;
}

.badge-text::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: #ff6b35;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 40px;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    font-size: 15px;
    transition: color 0.2s ease;
    position: relative;
}

.nav-links a:hover {
    color: #0f172a;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: #ff6b35;
    transition: width 0.2s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.demo-btn {
    background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
    color: white !important;
    padding: 12px 24px !important;
    border-radius: 10px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 14px 0 rgba(255, 107, 53, 0.25) !important;
}

.demo-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 8px 25px 0 rgba(255, 107, 53, 0.35) !important;
}

.demo-btn::after {
    display: none !important;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: #64748b;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 120px 0 80px;
}

/* Hero Background */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 107, 53, 0.05) 0%,
        rgba(247, 147, 30, 0.03) 25%,
        rgba(16, 185, 129, 0.03) 50%,
        rgba(245, 158, 11, 0.05) 75%,
        rgba(239, 68, 68, 0.03) 100%);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 20%;
    animation-delay: 2s;
}

.particle:nth-child(3) {
    top: 30%;
    right: 15%;
    animation-delay: 4s;
}

.particle:nth-child(4) {
    bottom: 40%;
    right: 25%;
    animation-delay: 1s;
}

.particle:nth-child(5) {
    bottom: 20%;
    left: 30%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* Hero Content */
.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
}

.hero-text {
    max-width: 600px;
}

/* Hero Announcement */
.hero-announcement {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.announcement-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid rgba(255, 107, 53, 0.2);
    backdrop-filter: blur(10px);
}

.badge-pulse {
    width: 8px;
    height: 8px;
    background: #ff6b35;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
    }
}

.social-proof-mini {
    display: flex;
    align-items: center;
    gap: 8px;
}

.proof-avatars {
    display: flex;
    margin-left: -8px;
}

.avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    margin-left: -8px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.avatar:nth-child(2) {
    background: linear-gradient(135deg, #10b981, #059669);
}

.avatar:nth-child(3) {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.proof-text {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

/* Hero Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: #0f172a;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.gradient-text-animated {
    background: linear-gradient(135deg, #ff6b35, #f7931e, #10b981, #f59e0b);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Hero Subtitle */
.hero-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 32px;
    max-width: 90%;
}

/* Quick Features */
.hero-features-quick {
    display: flex;
    gap: 24px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.quick-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(10px);
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
}

.feature-icon-mini {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

.ai-icon-mini {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.personalized-icon-mini {
    background: linear-gradient(135deg, #10b981, #059669);
}

.interactive-icon-mini {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 20px;
    margin-bottom: 48px;
    align-items: center;
}

.hero-cta-primary {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 18px 32px;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
}

.hero-cta-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(255, 107, 53, 0.4);
}

.cta-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-cta-secondary {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    color: #64748b;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.hero-cta-secondary:hover {
    color: #ff6b35;
}

.play-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 107, 53, 0.1);
    border: 2px solid rgba(255, 107, 53, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff6b35;
    transition: all 0.3s ease;
}

.hero-cta-secondary:hover .play-button {
    background: rgba(255, 107, 53, 0.2);
    transform: scale(1.1);
}

.hero-cta-secondary small {
    display: block;
    font-size: 12px;
    color: #94a3b8;
    font-weight: 400;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    height: 700px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Visual Container */
.visual-container {
    position: relative;
    width: 100%;
    height: 100%;
}

/* Interactive Book Demo */
.book-demo {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
    z-index: 3;
}

.book-cover {
    background: linear-gradient(135deg, #1e293b, #334155);
    border-radius: 20px;
    padding: 32px 24px;
    color: white;
    text-align: center;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid #475569;
    position: relative;
    overflow: hidden;
}

.book-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(16, 185, 129, 0.1));
    pointer-events: none;
}

.book-title {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.book-subtitle {
    font-size: 14px;
    color: #cbd5e1;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.ai-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 107, 53, 0.2);
    color: #ff6b35;
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 107, 53, 0.3);
    position: relative;
    z-index: 2;
}

/* Interaction Bubbles */
.interaction-bubbles {
    position: absolute;
    top: -20px;
    left: -40px;
    right: -40px;
    bottom: -20px;
    pointer-events: none;
}

.chat-bubble {
    position: absolute;
    display: flex;
    align-items: flex-end;
    gap: 12px;
    animation: bubbleFloat 3s ease-in-out infinite;
}

.chat-bubble.student {
    top: 20%;
    right: -20px;
    animation-delay: 0s;
}

.chat-bubble.ai {
    bottom: 20%;
    left: -20px;
    animation-delay: 1.5s;
}

@keyframes bubbleFloat {
    0%, 100% {
        transform: translateY(0px);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

.bubble-content {
    background: white;
    border-radius: 16px;
    padding: 12px 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    max-width: 200px;
    position: relative;
}

.chat-bubble.student .bubble-content {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: none;
}

.chat-bubble.ai .bubble-content {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
}

.bubble-content p {
    margin: 0;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.4;
}

.ai-response {
    display: none;
}

.chat-bubble.ai.typing .ai-response {
    display: block;
    animation: typeWriter 2s ease-in-out 2s forwards;
    opacity: 0;
}

@keyframes typeWriter {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.typing-indicator {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-indicator span {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    animation: typingDot 1.4s ease-in-out infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDot {
    0%, 60%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.bubble-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.student-avatar {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.ai-avatar {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Floating Features */
.floating-features {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    min-width: 180px;
    backdrop-filter: blur(10px);
}

.feature-card-1 {
    top: 10%;
    right: 10%;
    animation: floatCard1 6s ease-in-out infinite;
}

.feature-card-2 {
    bottom: 30%;
    right: 5%;
    animation: floatCard2 6s ease-in-out infinite 2s;
}

.feature-card-3 {
    top: 50%;
    left: -10%;
    animation: floatCard3 6s ease-in-out infinite 4s;
}

@keyframes floatCard1 {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-15px) rotate(1deg);
        opacity: 1;
    }
}

@keyframes floatCard2 {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-20px) rotate(-1deg);
        opacity: 1;
    }
}

@keyframes floatCard3 {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-10px) rotate(0.5deg);
        opacity: 1;
    }
}

.floating-card .card-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    color: white;
    font-size: 18px;
}

.feature-card-1 .card-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.feature-card-2 .card-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.feature-card-3 .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.floating-card .card-content h4 {
    font-size: 14px;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 6px;
    line-height: 1.3;
}

.floating-card .card-content p {
    font-size: 12px;
    color: #64748b;
    margin: 0;
    line-height: 1.4;
}

/* Success Popup */
.success-popup {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    min-width: 200px;
    animation: successPulse 4s ease-in-out infinite 3s;
    opacity: 0;
}

@keyframes successPulse {
    0%, 90%, 100% {
        opacity: 0;
        transform: translateX(-50%) scale(0.9);
    }
    10%, 80% {
        opacity: 1;
        transform: translateX(-50%) scale(1);
    }
}

.popup-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.success-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981, #059669);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.popup-header span {
    font-size: 14px;
    font-weight: 600;
    color: #0f172a;
}

.popup-stats {
    display: flex;
    gap: 16px;
}

.popup-stat {
    text-align: center;
}

.popup-number {
    display: block;
    font-size: 18px;
    font-weight: 800;
    color: #10b981;
    line-height: 1;
}

.popup-label {
    font-size: 11px;
    color: #64748b;
    font-weight: 500;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-bottom: 40px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(10px);
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats .stat-number {
    display: inline-block;
    font-size: 2.5rem;
    font-weight: 900;
    color: #0f172a;
    line-height: 1;
}

.stat-unit {
    font-size: 1.5rem;
    font-weight: 900;
    color: #ff6b35;
    margin-left: 2px;
}

.hero-stats .stat-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    margin-top: 8px;
}

/* Trusted By */
.trusted-by {
    margin-top: 40px;
}

.trusted-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 600;
    text-align: center;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.institution-logos {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
}

.institution-logo {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.institution-logo:hover {
    background: rgba(255, 107, 53, 0.1);
    border-color: rgba(255, 107, 53, 0.2);
    color: #ff6b35;
    transform: translateY(-2px);
}

/* Section Styles */
section {
    padding: 120px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    margin-bottom: 24px;
    color: #0f172a;
}

.section-header p {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.7;
}

/* Features Section */
.features {
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    padding: 32px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    border-color: #ff6b35;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

.feature-card.large {
    grid-row: span 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.feature-icon-wrapper {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
}

.feature-icon {
    width: 28px;
    height: 28px;
    border-radius: 8px;
}

.brain-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.analytics-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.integration-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 16px;
}

.feature-card p {
    color: #64748b;
    margin-bottom: 24px;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0 0 32px 0;
}

.feature-list li {
    padding: 8px 0;
    color: #64748b;
    position: relative;
    padding-left: 24px;
}

.feature-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 16px;
    width: 6px;
    height: 6px;
    background: #ff6b35;
    border-radius: 50%;
}

.feature-visual {
    margin-top: auto;
}

.ai-visualization {
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
}

.neural-network {
    position: relative;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.node {
    width: 12px;
    height: 12px;
    background: #e2e8f0;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.node.active {
    background: #ff6b35;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
    animation: pulse 2s infinite;
}

.connection {
    position: absolute;
    height: 2px;
    background: #e2e8f0;
    width: 20px;
    transition: all 0.3s ease;
}

.connection.active {
    background: #ff6b35;
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.mini-chart {
    height: 40px;
    background: #f8fafc;
    border-radius: 8px;
    margin-top: 16px;
    position: relative;
    overflow: hidden;
}

.chart-line {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.2), rgba(247, 147, 30, 0.2));
    border-radius: 8px 8px 0 0;
    animation: chartGrow 2s ease-out;
}

.integration-visual {
    margin-top: 16px;
}

.api-blocks {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.api-block {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-radius: 6px;
    animation: blockFloat 3s ease-in-out infinite;
}

.api-block:nth-child(2) {
    animation-delay: 0.5s;
}

.api-block:nth-child(3) {
    animation-delay: 1s;
}

@keyframes chartGrow {
    from { height: 0; }
    to { height: 60%; }
}

@keyframes blockFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
}

/* About Section */
.about {
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    font-size: 1.125rem;
    margin-bottom: 30px;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
}

.feature i {
    color: #10b981;
    font-size: 18px;
}

.feature span {
    color: #374151;
    font-weight: 500;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

.timeline-item {
    position: relative;
    margin-bottom: 40px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    background: #4f46e5;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #4f46e5;
}

.timeline-year {
    font-size: 1.25rem;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 5px;
}

.timeline-content {
    color: #6b7280;
    font-weight: 500;
}

/* Products Section */
.products {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
}

.products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* Product Navigation Tabs */
.product-tabs {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 48px;
    background: white;
    padding: 8px;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 48px;
}

.product-tab {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    border: none;
    background: transparent;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    justify-content: center;
    min-height: 80px;
}

.product-tab:hover {
    background: rgba(255, 107, 53, 0.05);
}

.product-tab.active {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.tab-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.book-tab-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.publisher-tab-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.platform-tab-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.product-tab.active .tab-icon {
    background: rgba(255, 255, 255, 0.2);
}

.product-tab span {
    font-weight: 600;
    font-size: 14px;
}

.tab-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
    white-space: nowrap;
}

.product-tab.active .tab-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #ff6b35;
}

/* Products Showcase */
.products-showcase {
    position: relative;
    min-height: 600px;
}

.product-showcase {
    display: none;
    animation: fadeInUp 0.6s ease-out;
}

.product-showcase.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.showcase-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 32px;
    max-width: 1400px;
    margin: 0 auto;
    align-items: start;
}

/* Hero Product Card */
.product-hero-card {
    background: white;
    border-radius: 24px;
    padding: 40px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    transition: all 0.4s ease;
}

.product-hero-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.25);
}

.product-hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.ibookgpt-theme::before {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.gptsir-theme::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.whitelabel-theme::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.hero-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
}

.hero-icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    position: relative;
}

.hero-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.book-hero-icon {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.publisher-hero-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.platform-hero-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.hero-badge {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    padding: 8px 16px;
    border-radius: 24px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 107, 53, 0.2);
}

.hero-content h3 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #0f172a;
    margin-bottom: 12px;
    line-height: 1.2;
}

.hero-subtitle {
    color: #ff6b35;
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 20px !important;
}

.hero-description {
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 32px;
    font-size: 16px;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;
    padding: 24px;
    background: #f8fafc;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #0f172a;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: 16px;
    align-items: center;
}

.primary-cta {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 18px 32px;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.primary-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

.gptsir-cta {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.gptsir-cta:hover {
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.whitelabel-cta {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.whitelabel-cta:hover {
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.secondary-cta {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    padding: 18px 24px;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    transition: all 0.3s ease;
}

.secondary-cta:hover {
    border-color: #ff6b35;
    color: #ff6b35;
    background: rgba(255, 107, 53, 0.05);
}

/* Trusted Publishers Showcase */
.trusted-publishers-showcase {
    margin-bottom: 32px;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

.trusted-publishers-showcase .trusted-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 600;
    margin-bottom: 12px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
}

.publisher-logos {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.publisher-logo {
    background: white;
    color: #64748b;
    padding: 8px 16px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.publisher-logo:hover {
    border-color: #10b981;
    color: #10b981;
    transform: translateY(-1px);
}
/* Enterprise Features */
.enterprise-features {
    display: flex;
    gap: 16px;
    margin-bottom: 32px;
    padding: 20px;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-radius: 16px;
    border: 1px solid #f59e0b;
}

.enterprise-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #92400e;
    font-weight: 600;
    font-size: 12px;
}

.enterprise-feature i {
    color: #f59e0b;
}

/* Feature Cards in Showcase */
.showcase-features {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.feature-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    padding: 24px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    border-color: #ff6b35;
    box-shadow: 0 12px 24px -4px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff6b35, #f7931e);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    color: white;
    font-size: 20px;
}

.ai-gradient {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.personalized-gradient, .analytics-gradient {
    background: linear-gradient(135deg, #10b981, #059669);
}

.interactive-gradient, .brand-gradient {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.platform-gradient, .enterprise-gradient {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.feature-card h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 12px;
    line-height: 1.3;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
    font-size: 14px;
    margin: 0;
}

/* Why It Matters Section */
.why-matters {
    background: #f8fafc;
}

.stakeholders-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.stakeholder-card {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.stakeholder-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stakeholder-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.stakeholder-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 15px;
}

.stakeholder-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Integration Section */
.integration {
    background: white;
}

.integration-content {
    max-width: 1000px;
    margin: 0 auto;
}

.integration-benefits {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 50px;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 30px;
    background: #f8fafc;
    border-radius: 16px;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.benefit-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.benefit-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
}

.benefit-text p {
    color: #6b7280;
    margin: 0;
}

.integration-cta {
    text-align: center;
    padding: 40px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 20px;
    color: white;
}

.integration-cta h3 {
    font-size: 1.75rem;
    margin-bottom: 20px;
    color: white;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.cta-buttons .btn {
    background: white;
    color: #4f46e5;
    border: 2px solid white;
}

.cta-buttons .btn:hover {
    background: transparent;
    color: white;
    border-color: white;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.cta-content {
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #1f2937;
}

.cta-content > p {
    font-size: 1.25rem;
    margin-bottom: 50px;
    color: #6b7280;
}

.cta-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.cta-option {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.cta-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.cta-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.cta-option h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 10px;
}

.cta-option p {
    color: #6b7280;
    margin-bottom: 25px;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 800;
    margin-bottom: 20px;
}

.footer-logo-img {
    height: 60px;
    width: auto;
    object-fit: contain;
}

.footer-logo .logo-icon {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

.footer-description {
    color: #9ca3af;
    margin-bottom: 25px;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #ff6b35;
    transform: translateY(-2px);
}

.footer-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #ff6b35;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 30px;
    text-align: center;
}

.footer-bottom p {
    color: #9ca3af;
    margin: 0;
}

/* Welcome Banner */
.welcome-banner {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    padding: 15px 25px;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
    z-index: 1000;
    animation: slideIn 0.5s ease-out;
}

.welcome-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.welcome-icon {
    font-size: 20px;
}

.welcome-text {
    font-weight: 600;
    font-size: 14px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 20px;
    }

    h1 {
        font-size: 3rem;
    }

    h2 {
        font-size: 2.5rem;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-announcement {
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    .hero-features-quick {
        justify-content: center;
    }

    .hero-actions {
        flex-direction: column;
        gap: 16px;
        width: 100%;
    }

    .hero-cta-primary,
    .hero-cta-secondary {
        width: 100%;
        justify-content: center;
    }

    .hero-visual {
        height: 500px;
    }

    .floating-features {
        display: none;
    }

    .book-demo {
        max-width: 300px;
    }

    .interaction-bubbles {
        left: -20px;
        right: -20px;
    }

    .chat-bubble {
        position: relative;
        margin-bottom: 16px;
    }

    .chat-bubble.student {
        top: auto;
        right: auto;
    }

    .chat-bubble.ai {
        bottom: auto;
        left: auto;
    }

    .dashboard-mockup {
        max-width: 400px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card.large {
        grid-row: span 1;
    }

    /* Product Tabs Mobile */
    .product-tabs {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
        max-width: 100%;
    }

    .product-tab {
        padding: 16px;
        min-height: auto;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .tab-badge {
        position: static;
        margin-top: 4px;
    }

    /* Products Showcase Mobile */
    .showcase-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .product-hero-card {
        padding: 24px;
    }

    .hero-content h3 {
        font-size: 2rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 16px;
        text-align: center;
    }

    .hero-actions {
        flex-direction: column;
        gap: 12px;
    }

    .primary-cta, .secondary-cta {
        width: 100%;
        justify-content: center;
    }

    .showcase-features {
        gap: 16px;
    }

    .feature-card {
        padding: 20px;
    }

    .enterprise-features {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .publisher-logos {
        gap: 8px;
    }

    .publisher-logo {
        font-size: 11px;
        padding: 6px 12px;
    }

    .stakeholders-grid {
        grid-template-columns: 1fr 1fr;
    }

    .integration-benefits {
        grid-template-columns: 1fr;
    }

    .cta-options {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    .hero {
        padding: 100px 0 60px;
        min-height: auto;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-features-quick {
        gap: 12px;
    }

    .quick-feature {
        font-size: 12px;
        padding: 6px 12px;
    }

    .hero-actions {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .hero-cta-primary {
        padding: 16px 24px;
        font-size: 14px;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .hero-stats .stat-number {
        font-size: 2rem;
    }

    .hero-visual {
        height: 400px;
    }

    .book-demo {
        max-width: 280px;
    }

    .book-cover {
        padding: 24px 20px;
    }

    .book-title {
        font-size: 1.25rem;
    }

    .success-popup {
        min-width: 160px;
        padding: 16px;
    }

    .institution-logos {
        gap: 12px;
    }

    .institution-logo {
        font-size: 11px;
        padding: 6px 12px;
    }

    .dashboard-mockup {
        max-width: 320px;
    }

    .mockup-content {
        padding: 16px;
    }

    .analytics-card {
        padding: 16px;
    }

    section {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .feature-card {
        padding: 24px;
    }

    /* Additional Mobile Optimizations for Products */
    .product-tabs {
        padding: 8px;
        gap: 8px;
    }

    .product-tab {
        padding: 12px 16px;
        font-size: 13px;
    }

    .tab-icon {
        width: 20px;
        height: 20px;
    }

    .product-hero-card {
        padding: 20px;
    }

    .hero-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .hero-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }

    .hero-content h3 {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .hero-description {
        font-size: 14px;
    }

    .feature-icon-wrapper {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .feature-card h4 {
        font-size: 1rem;
    }

    .feature-card p {
        font-size: 13px;
    }

    .stakeholders-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .stakeholder-card {
        padding: 24px;
    }

    .integration-benefits {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .benefit-item {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .integration-cta {
        padding: 24px;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .cta-options {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .cta-option {
        padding: 24px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 32px;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    .welcome-banner {
        bottom: 20px;
        right: 20px;
        padding: 12px 20px;
    }

    .welcome-text {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.75rem;
    }

    .hero {
        padding: 80px 0 40px;
    }

    .hero-title {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-features-quick {
        flex-direction: column;
        gap: 8px;
        align-items: center;
    }

    .hero-stats {
        gap: 16px;
        padding: 16px;
    }

    .hero-stats .stat-number {
        font-size: 1.75rem;
    }

    .stat-unit {
        font-size: 1.25rem;
    }

    .hero-visual {
        height: 300px;
    }

    .book-demo {
        max-width: 240px;
    }

    .book-cover {
        padding: 20px 16px;
    }

    .book-title {
        font-size: 1.1rem;
    }

    .book-subtitle {
        font-size: 12px;
    }

    .ai-badge {
        font-size: 10px;
        padding: 6px 12px;
    }

    .bubble-content {
        max-width: 150px;
        padding: 10px 12px;
    }

    .bubble-content p {
        font-size: 11px;
    }

    .bubble-avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .floating-card {
        min-width: 140px;
        padding: 12px;
    }



    .stakeholder-card {
        padding: 25px 15px;
    }

    .stakeholder-icon {
        font-size: 36px;
    }

    .benefit-item {
        padding: 15px;
    }

    .benefit-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .integration-cta {
        padding: 25px 15px;
    }

    .cta-option {
        padding: 25px 15px;
    }

    .cta-icon {
        font-size: 36px;
    }

    .welcome-banner {
        bottom: 15px;
        right: 15px;
        padding: 10px 15px;
    }

    .welcome-icon {
        font-size: 16px;
    }

    .welcome-text {
        font-size: 11px;
    }
}
