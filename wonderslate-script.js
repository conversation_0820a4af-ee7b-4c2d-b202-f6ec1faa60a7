// Wonderslate Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Header scroll effect
    const header = document.querySelector('.header');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.backdropFilter = 'blur(20px)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        }
        
        // Hide/show header on scroll
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinksContainer = document.querySelector('.nav-links');
    
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            navLinksContainer.classList.toggle('mobile-menu-open');
            this.classList.toggle('active');
        });
    }

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.product-card, .stakeholder-card, .benefit-item, .cta-option, .timeline-item');
    animateElements.forEach(el => {
        observer.observe(el);
    });

    // Counter animation for stats
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start).toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toLocaleString();
            }
        }
        
        updateCounter();
    }

    // Animate stats when they come into view
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.querySelector('.stat-number');
                const text = statNumber.textContent;
                
                if (text.includes('M+')) {
                    const number = parseInt(text.replace('M+', ''));
                    animateCounter(statNumber, number, 2000);
                    statNumber.textContent = number + 'M+';
                } else if (text.includes('+')) {
                    const number = parseInt(text.replace('+', ''));
                    animateCounter(statNumber, number, 2000);
                    statNumber.textContent = number + '+';
                } else {
                    const number = parseInt(text);
                    animateCounter(statNumber, number, 2000);
                }
                
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    const stats = document.querySelectorAll('.stat');
    stats.forEach(stat => {
        statsObserver.observe(stat);
    });

    // Floating cards animation
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 2}s`;
    });

    // Welcome banner auto-hide
    const welcomeBanner = document.querySelector('.welcome-banner');
    if (welcomeBanner) {
        setTimeout(() => {
            welcomeBanner.style.opacity = '0';
            welcomeBanner.style.transform = 'translateX(100%)';
            setTimeout(() => {
                welcomeBanner.style.display = 'none';
            }, 500);
        }, 5000);
    }

    // Form handling for demo requests
    const demoButtons = document.querySelectorAll('a[href^="mailto:"]');
    demoButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Track demo request
            if (typeof gtag !== 'undefined') {
                gtag('event', 'demo_request', {
                    'event_category': 'engagement',
                    'event_label': this.textContent
                });
            }
        });
    });

    // Parallax effect for hero section
    const hero = document.querySelector('.hero');
    const heroContent = document.querySelector('.hero-content');
    
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        if (hero && scrolled < hero.offsetHeight) {
            heroContent.style.transform = `translateY(${rate}px)`;
        }
    });

    // Add loading animation
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
        
        // Animate hero elements
        setTimeout(() => {
            const heroText = document.querySelector('.hero-text');
            const heroVisual = document.querySelector('.hero-visual');
            
            if (heroText) heroText.classList.add('animate-in');
            if (heroVisual) heroVisual.classList.add('animate-in');
        }, 300);
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close mobile menu if open
            if (navLinksContainer && navLinksContainer.classList.contains('mobile-menu-open')) {
                navLinksContainer.classList.remove('mobile-menu-open');
                mobileMenuToggle.classList.remove('active');
            }
        }
    });

    // Preload critical images
    const criticalImages = [
        // Add any critical image URLs here
    ];
    
    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });

    // Add scroll progress indicator
    const scrollProgress = document.createElement('div');
    scrollProgress.className = 'scroll-progress';
    scrollProgress.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: #ff6b35;
        z-index: 9999;
        transition: width 0.1s ease;
    `;
    document.body.appendChild(scrollProgress);

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';
    });

    // Performance optimization: Debounce scroll events
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Apply debouncing to scroll events
    const debouncedScrollHandler = debounce(function() {
        // Any heavy scroll operations can go here
    }, 10);

    window.addEventListener('scroll', debouncedScrollHandler);

    // Product Tabs Functionality
    const productTabs = document.querySelectorAll('.product-tab');
    const productShowcases = document.querySelectorAll('.product-showcase');

    function switchProduct(targetProduct) {
        // Remove active class from all tabs and showcases
        productTabs.forEach(tab => tab.classList.remove('active'));
        productShowcases.forEach(showcase => showcase.classList.remove('active'));

        // Add active class to selected tab and showcase
        const activeTab = document.querySelector(`[data-product="${targetProduct}"]`);
        const activeShowcase = document.getElementById(`${targetProduct}-showcase`);

        if (activeTab && activeShowcase) {
            activeTab.classList.add('active');
            activeShowcase.classList.add('active');

            // Trigger animation for stats in the active showcase
            const statNumbers = activeShowcase.querySelectorAll('.stat-number');
            statNumbers.forEach(statNumber => {
                const text = statNumber.textContent;

                // Reset and animate the counter
                setTimeout(() => {
                    if (text.includes('K+')) {
                        const number = parseInt(text.replace('K+', ''));
                        animateCounter(statNumber, number, 1500);
                        statNumber.textContent = number + 'K+';
                    } else if (text.includes('%')) {
                        const number = parseInt(text.replace('%', ''));
                        animateCounter(statNumber, number, 1500);
                        statNumber.textContent = number + '%';
                    } else if (text.includes('hrs')) {
                        const number = parseInt(text.replace('hrs', ''));
                        animateCounter(statNumber, number, 1500);
                        statNumber.textContent = number + 'hrs';
                    } else if (text === '∞') {
                        // Special case for infinity symbol
                        statNumber.textContent = '∞';
                    } else if (text.includes('+')) {
                        const number = parseInt(text.replace('+', ''));
                        animateCounter(statNumber, number, 1500);
                        statNumber.textContent = number + '+';
                    } else if (text.includes('Days')) {
                        const number = parseInt(text.replace(' Days', ''));
                        animateCounter(statNumber, number, 1500);
                        statNumber.textContent = number + ' Days';
                    } else if (text.includes('.')) {
                        // Handle decimal numbers like 99.9%
                        const number = parseFloat(text.replace('%', ''));
                        animateDecimalCounter(statNumber, number, 1500);
                        statNumber.textContent = number + '%';
                    }
                }, 200);
            });

            // Track product tab interaction
            if (typeof gtag !== 'undefined') {
                gtag('event', 'product_tab_click', {
                    'event_category': 'engagement',
                    'event_label': targetProduct
                });
            }
        }
    }

    // Decimal counter animation for numbers like 99.9
    function animateDecimalCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);

        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = start.toFixed(1);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toFixed(1);
            }
        }

        updateCounter();
    }

    // Add click event listeners to product tabs
    productTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetProduct = this.getAttribute('data-product');
            switchProduct(targetProduct);
        });
    });

    // Keyboard navigation for product tabs
    document.addEventListener('keydown', function(e) {
        const activeTab = document.querySelector('.product-tab.active');
        if (!activeTab) return;

        let nextTab = null;
        const currentIndex = Array.from(productTabs).indexOf(activeTab);

        if (e.key === 'ArrowLeft' && currentIndex > 0) {
            nextTab = productTabs[currentIndex - 1];
        } else if (e.key === 'ArrowRight' && currentIndex < productTabs.length - 1) {
            nextTab = productTabs[currentIndex + 1];
        }

        if (nextTab) {
            e.preventDefault();
            const targetProduct = nextTab.getAttribute('data-product');
            switchProduct(targetProduct);
            nextTab.focus();
        }
    });

    // Auto-rotate products (optional - can be enabled/disabled)
    let autoRotateInterval;
    const enableAutoRotate = false; // Set to true to enable auto-rotation

    if (enableAutoRotate) {
        let currentProductIndex = 0;
        const products = ['ibookgpt', 'gptsir', 'whitelabel'];

        autoRotateInterval = setInterval(() => {
            currentProductIndex = (currentProductIndex + 1) % products.length;
            switchProduct(products[currentProductIndex]);
        }, 8000); // Rotate every 8 seconds

        // Pause auto-rotation when user interacts with tabs
        productTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                clearInterval(autoRotateInterval);
            });
        });
    }

    // Initialize first product showcase with animation
    setTimeout(() => {
        const firstShowcase = document.querySelector('.product-showcase.active');
        if (firstShowcase) {
            const statNumbers = firstShowcase.querySelectorAll('.stat-number');
            statNumbers.forEach(statNumber => {
                const text = statNumber.textContent;
                if (text.includes('K+')) {
                    const number = parseInt(text.replace('K+', ''));
                    animateCounter(statNumber, number, 2000);
                    statNumber.textContent = number + 'K+';
                } else if (text.includes('%')) {
                    const number = parseInt(text.replace('%', ''));
                    animateCounter(statNumber, number, 2000);
                    statNumber.textContent = number + '%';
                }
            });
        }
    }, 1000);

    // Hero Stats Animation
    function animateHeroStats() {
        const heroStats = document.querySelectorAll('.hero-stats .stat-number');
        heroStats.forEach(statElement => {
            const target = parseInt(statElement.getAttribute('data-target'));
            if (target) {
                animateCounter(statElement, target, 2500);
            }
        });
    }

    // Hero Interactive Elements
    function initializeHeroInteractions() {
        // Animate typing indicator and AI response
        setTimeout(() => {
            const typingBubble = document.querySelector('.chat-bubble.ai');
            if (typingBubble) {
                typingBubble.classList.add('typing');

                // Hide typing indicator and show response after delay
                setTimeout(() => {
                    const typingIndicator = typingBubble.querySelector('.typing-indicator');
                    const aiResponse = typingBubble.querySelector('.ai-response');

                    if (typingIndicator && aiResponse) {
                        typingIndicator.style.display = 'none';
                        aiResponse.style.display = 'block';
                    }
                }, 3000);
            }
        }, 2000);

        // Animate hero stats when they come into view
        const heroStatsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateHeroStats();
                    heroStatsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const heroStatsSection = document.querySelector('.hero-stats');
        if (heroStatsSection) {
            heroStatsObserver.observe(heroStatsSection);
        }

        // Add hover effects to floating cards
        const floatingCards = document.querySelectorAll('.floating-card');
        floatingCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.05)';
                this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.2)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });

        // Parallax effect for hero particles
        window.addEventListener('mousemove', function(e) {
            const particles = document.querySelectorAll('.particle');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            particles.forEach((particle, index) => {
                const speed = (index + 1) * 0.5;
                const x = (mouseX - 0.5) * speed * 20;
                const y = (mouseY - 0.5) * speed * 20;

                particle.style.transform = `translate(${x}px, ${y}px)`;
            });
        });

        // Add click tracking for hero CTAs
        const heroCTAs = document.querySelectorAll('.hero-cta-primary, .hero-cta-secondary');
        heroCTAs.forEach(cta => {
            cta.addEventListener('click', function(e) {
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                this.appendChild(ripple);

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';

                setTimeout(() => {
                    ripple.remove();
                }, 600);

                // Track interaction
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'hero_cta_click', {
                        'event_category': 'engagement',
                        'event_label': this.textContent.trim()
                    });
                }
            });
        });
    }

    // Initialize hero interactions
    initializeHeroInteractions();

    // Add CSS for ripple effect
    const rippleCSS = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: rippleEffect 0.6s linear;
            pointer-events: none;
        }

        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;

    const style = document.createElement('style');
    style.textContent = rippleCSS;
    document.head.appendChild(style);

    console.log('🌟 Wonderslate landing page loaded successfully!');
});
